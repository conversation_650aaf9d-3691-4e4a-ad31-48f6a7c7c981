{"name": "auth-tutorial", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "NODE_ENV=development nodemon index.js", "start": "node start.js", "start:direct": "NODE_ENV=production node index.js", "build": "npm install && npm install --prefix frontend && npm run build --prefix frontend", "seed:keys": "node scripts/seedKeys.js", "import:keys": "node scripts/importApiKeys.js", "test:db": "node scripts/testConnection.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "nodemailer": "^7.0.5", "validator": "^13.15.15"}, "devDependencies": {"nodemon": "^3.1.4"}}