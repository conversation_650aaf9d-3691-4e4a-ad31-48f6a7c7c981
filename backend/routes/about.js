// routes/about.js
import express from "express";
const router = express.Router();

router.get("/", (req, res) => {
  res.json({
    project: {
      title: "VNR-Keys",
      description:
        "A smart key management system designed to streamline key allocation, tracking, and return processes for improved security and efficiency.",
    },
    team: [
      {
        name: "<PERSON><PERSON><PERSON>",
        role: "Full Stack Developer",
        avatar: "/karthik.png",
        socials: { github: "#", linkedin: "#", twitter: "#" },
      },
      {
        name: "<PERSON>",
        role: "Frontend Developer",
        avatar: "/images/vishnu.jpg",
        socials: { github: "#", linkedin: "#", twitter: "#" },
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        role: "Backend Developer",
        avatar: "/images/bhavishwa.jpg",
        socials: { github: "#", linkedin: "#", twitter: "#" },
      },
      {
        name: "<PERSON>",
        role: "Designer & Frontend Developer",
        avatar: "/images/shiva.jpg",
        socials: { github: "#", linkedin: "#", twitter: "#" },
      },
    ],
  });
});

export default router;